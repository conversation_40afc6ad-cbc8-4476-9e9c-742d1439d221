package com.czc.blog.interceptor;

import com.czc.blog.service.SiteSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 网站设置拦截器
 * 用于向所有页面注入网站配置信息
 */
@Component
public class SiteSettingInterceptor implements HandlerInterceptor {

    @Autowired
    private SiteSettingService siteSettingService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return true;
    }
    
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        if (modelAndView != null) {
            // 获取所有网站设置并添加到request中
            Map<String, String> settingsMap = siteSettingService.getSiteSettingsMap();
            for (Map.Entry<String, String> entry : settingsMap.entrySet()) {
                request.setAttribute(entry.getKey(), entry.getValue());
            }
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    }
}