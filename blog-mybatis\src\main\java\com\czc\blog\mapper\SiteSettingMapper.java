package com.czc.blog.mapper;

import com.czc.blog.po.SiteSetting;
import com.czc.blog.po.SiteSettingExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SiteSettingMapper {
    long countByExample(SiteSettingExample example);

    int deleteByExample(SiteSettingExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SiteSetting record);

    int insertSelective(SiteSetting record);

    List<SiteSetting> selectByExample(SiteSettingExample example);

    SiteSetting selectByPrimaryKey(Long id);
    
    SiteSetting selectBySettingKey(String settingKey);

    int updateByExampleSelective(@Param("record") SiteSetting record, @Param("example") SiteSettingExample example);

    int updateByExample(@Param("record") SiteSetting record, @Param("example") SiteSettingExample example);

    int updateByPrimaryKeySelective(SiteSetting record);

    int updateByPrimaryKey(SiteSetting record);
}