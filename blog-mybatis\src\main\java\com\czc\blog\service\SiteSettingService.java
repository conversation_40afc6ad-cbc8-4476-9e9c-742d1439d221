package com.czc.blog.service;

import com.czc.blog.po.SiteSetting;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 网站设置服务接口
 */
public interface SiteSettingService {
    
    /**
     * 获取所有网站设置
     * @return 网站设置列表
     */
    List<SiteSetting> listSiteSettings();
    
    /**
     * 分页获取网站设置
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    PageInfo<SiteSetting> listSiteSettings(int pageNum, int pageSize);
    
    /**
     * 根据主键获取网站设置
     * @param id 主键
     * @return 网站设置
     */
    SiteSetting getSiteSetting(Long id);
    
    /**
     * 根据键名获取网站设置
     * @param key 键名
     * @return 网站设置
     */
    SiteSetting getSiteSettingByKey(String key);
    
    /**
     * 根据键名获取网站设置值
     * @param key 键名
     * @return 设置值
     */
    String getSiteSettingValue(String key);
    
    /**
     * 获取所有网站设置的键值对
     * @return 键值对Map
     */
    Map<String, String> getSiteSettingsMap();
    
    /**
     * 保存网站设置
     * @param siteSetting 网站设置
     * @return 是否成功
     */
    boolean saveSiteSetting(SiteSetting siteSetting);
    
    /**
     * 更新网站设置
     * @param id 主键
     * @param siteSetting 网站设置
     * @return 是否成功
     */
    boolean updateSiteSetting(Long id, SiteSetting siteSetting);
    
    /**
     * 根据键名更新设置值
     * @param key 键名
     * @param value 值
     * @return 是否成功
     */
    boolean updateSiteSettingValue(String key, String value);
    
    /**
     * 批量更新网站设置
     * @param settings 设置Map（键值对形式）
     * @return 是否成功
     */
    boolean updateSiteSettingValues(Map<String, String> settings);
    
    /**
     * 删除网站设置
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteSiteSetting(Long id);
}