package com.czc.blog.service;

import com.czc.blog.po.SocialLink;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 社交链接服务接口
 */
public interface SocialLinkService {
    
    /**
     * 获取所有社交链接
     * @return 社交链接列表
     */
    List<SocialLink> listAllSocialLinks();
    
    /**
     * 获取所有启用的社交链接
     * @return 启用的社交链接列表
     */
    List<SocialLink> listEnabledSocialLinks();
    
    /**
     * 分页获取社交链接
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    PageInfo<SocialLink> listSocialLinks(int pageNum, int pageSize);
    
    /**
     * 根据ID获取社交链接
     * @param id 链接ID
     * @return 社交链接
     */
    SocialLink getSocialLink(Long id);
    
    /**
     * 保存社交链接
     * @param socialLink 社交链接
     * @return 是否成功
     */
    boolean saveSocialLink(SocialLink socialLink);
    
    /**
     * 更新社交链接
     * @param id 主键
     * @param socialLink 社交链接
     * @return 是否成功
     */
    boolean updateSocialLink(Long id, SocialLink socialLink);
    
    /**
     * 启用或禁用社交链接
     * @param id 主键
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean toggleSocialLink(Long id, boolean enabled);
    
    /**
     * 删除社交链接
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteSocialLink(Long id);
}