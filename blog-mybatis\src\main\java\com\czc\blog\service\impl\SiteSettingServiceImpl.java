package com.czc.blog.service.impl;

import com.czc.blog.mapper.SiteSettingMapper;
import com.czc.blog.po.SiteSetting;
import com.czc.blog.po.SiteSettingExample;
import com.czc.blog.service.SiteSettingService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网站设置服务实现类
 */
@Service
public class SiteSettingServiceImpl implements SiteSettingService {
    
    @Autowired
    private SiteSettingMapper siteSettingMapper;

    @Override
    public List<SiteSetting> listSiteSettings() {
        SiteSettingExample example = new SiteSettingExample();
        example.setOrderByClause("id ASC");
        return siteSettingMapper.selectByExample(example);
    }

    @Override
    public PageInfo<SiteSetting> listSiteSettings(int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        SiteSettingExample example = new SiteSettingExample();
        example.setOrderByClause("id ASC");
        List<SiteSetting> siteSettings = siteSettingMapper.selectByExample(example);
        return new PageInfo<>(siteSettings);
    }

    @Override
    public SiteSetting getSiteSetting(Long id) {
        return siteSettingMapper.selectByPrimaryKey(id);
    }

    @Override
    public SiteSetting getSiteSettingByKey(String key) {
        return siteSettingMapper.selectBySettingKey(key);
    }

    @Override
    public String getSiteSettingValue(String key) {
        SiteSetting siteSetting = siteSettingMapper.selectBySettingKey(key);
        return siteSetting != null ? siteSetting.getSettingValue() : null;
    }

    @Override
    public Map<String, String> getSiteSettingsMap() {
        List<SiteSetting> siteSettings = listSiteSettings();
        Map<String, String> settingsMap = new HashMap<>();
        for (SiteSetting siteSetting : siteSettings) {
            settingsMap.put(siteSetting.getSettingKey(), siteSetting.getSettingValue());
        }
        return settingsMap;
    }

    @Transactional
    @Override
    public boolean saveSiteSetting(SiteSetting siteSetting) {
        siteSetting.setCreateTime(new Date());
        siteSetting.setUpdateTime(new Date());
        return siteSettingMapper.insert(siteSetting) > 0;
    }

    @Transactional
    @Override
    public boolean updateSiteSetting(Long id, SiteSetting siteSetting) {
        siteSetting.setId(id);
        siteSetting.setUpdateTime(new Date());
        return siteSettingMapper.updateByPrimaryKeySelective(siteSetting) > 0;
    }

    @Transactional
    @Override
    public boolean updateSiteSettingValue(String key, String value) {
        SiteSetting siteSetting = siteSettingMapper.selectBySettingKey(key);
        if (siteSetting == null) {
            return false;
        }
        siteSetting.setSettingValue(value);
        siteSetting.setUpdateTime(new Date());
        return siteSettingMapper.updateByPrimaryKeySelective(siteSetting) > 0;
    }

    @Transactional
    @Override
    public boolean updateSiteSettingValues(Map<String, String> settings) {
        boolean success = true;
        for (Map.Entry<String, String> entry : settings.entrySet()) {
            if (!updateSiteSettingValue(entry.getKey(), entry.getValue())) {
                success = false;
            }
        }
        return success;
    }

    @Transactional
    @Override
    public boolean deleteSiteSetting(Long id) {
        return siteSettingMapper.deleteByPrimaryKey(id) > 0;
    }
}