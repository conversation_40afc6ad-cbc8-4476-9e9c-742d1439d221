package com.czc.blog.service.impl;

import com.czc.blog.mapper.SocialLinkMapper;
import com.czc.blog.po.SocialLink;
import com.czc.blog.po.SocialLinkExample;
import com.czc.blog.service.SocialLinkService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 社交链接服务实现类
 */
@Service
public class SocialLinkServiceImpl implements SocialLinkService {

    @Autowired
    private SocialLinkMapper socialLinkMapper;

    @Override
    public List<SocialLink> listAllSocialLinks() {
        SocialLinkExample example = new SocialLinkExample();
        example.setOrderByClause("sort ASC");
        return socialLinkMapper.selectByExample(example);
    }

    @Override
    public List<SocialLink> listEnabledSocialLinks() {
        SocialLinkExample example = new SocialLinkExample();
        example.createCriteria().andEnabledEqualTo(true);
        example.setOrderByClause("sort ASC");
        return socialLinkMapper.selectByExample(example);
    }

    @Override
    public PageInfo<SocialLink> listSocialLinks(int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        SocialLinkExample example = new SocialLinkExample();
        example.setOrderByClause("sort ASC");
        List<SocialLink> socialLinks = socialLinkMapper.selectByExample(example);
        return new PageInfo<>(socialLinks);
    }

    @Override
    public SocialLink getSocialLink(Long id) {
        return socialLinkMapper.selectByPrimaryKey(id);
    }

    @Transactional
    @Override
    public boolean saveSocialLink(SocialLink socialLink) {
        if (socialLink.getSort() == null) {
            socialLink.setSort(0);
        }
        if (socialLink.getEnabled() == null) {
            socialLink.setEnabled(true);
        }
        if (socialLink.getTargetBlank() == null) {
            socialLink.setTargetBlank(true);
        }
        if (socialLink.getPopup() == null) {
            socialLink.setPopup(false);
        }
        socialLink.setCreateTime(new Date());
        socialLink.setUpdateTime(new Date());
        return socialLinkMapper.insert(socialLink) > 0;
    }

    @Transactional
    @Override
    public boolean updateSocialLink(Long id, SocialLink socialLink) {
        socialLink.setId(id);
        socialLink.setUpdateTime(new Date());
        return socialLinkMapper.updateByPrimaryKeySelective(socialLink) > 0;
    }

    @Transactional
    @Override
    public boolean toggleSocialLink(Long id, boolean enabled) {
        SocialLink socialLink = socialLinkMapper.selectByPrimaryKey(id);
        if (socialLink == null) {
            return false;
        }
        socialLink.setEnabled(enabled);
        socialLink.setUpdateTime(new Date());
        return socialLinkMapper.updateByPrimaryKeySelective(socialLink) > 0;
    }

    @Transactional
    @Override
    public boolean deleteSocialLink(Long id) {
        return socialLinkMapper.deleteByPrimaryKey(id) > 0;
    }
}