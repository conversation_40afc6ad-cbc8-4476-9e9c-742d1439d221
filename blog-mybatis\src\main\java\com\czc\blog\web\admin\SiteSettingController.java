package com.czc.blog.web.admin;

import com.czc.blog.po.SiteSetting;
import com.czc.blog.service.SiteSettingService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.HashMap;
import java.util.Map;

/**
 * 网站设置管理控制器
 */
@Controller
@RequestMapping("/admin/settings")
public class SiteSettingController {
    
    @Autowired
    private SiteSettingService siteSettingService;
    
    /**
     * 网站设置列表页
     */
    @GetMapping
    public String settings(@RequestParam(defaultValue = "1") int page,
                          @RequestParam(defaultValue = "10") int size,
                          Model model) {
        PageInfo<SiteSetting> pageInfo = siteSettingService.listSiteSettings(page, size);
        model.addAttribute("page", pageInfo);
        return "admin/settings";
    }
    
    /**
     * 跳转到新增网站设置页面
     */
    @GetMapping("/input")
    public String input(Model model) {
        model.addAttribute("siteSetting", new SiteSetting());
        return "admin/settings-input";
    }
    
    /**
     * 跳转到编辑网站设置页面
     */
    @GetMapping("/{id}/input")
    public String editInput(@PathVariable Long id, Model model) {
        model.addAttribute("siteSetting", siteSettingService.getSiteSetting(id));
        return "admin/settings-input";
    }
    
    /**
     * 新增或编辑网站设置
     */
    @PostMapping("/post")
    public String post(SiteSetting siteSetting, RedirectAttributes attributes) {
        boolean success;
        if (siteSetting.getId() == null) {
            success = siteSettingService.saveSiteSetting(siteSetting);
        } else {
            success = siteSettingService.updateSiteSetting(siteSetting.getId(), siteSetting);
        }
        
        if (success) {
            attributes.addFlashAttribute("message", "操作成功");
        } else {
            attributes.addFlashAttribute("message", "操作失败");
        }
        
        return "redirect:/admin/settings";
    }
    
    /**
     * 删除网站设置
     */
    @GetMapping("/{id}/delete")
    public String delete(@PathVariable Long id, RedirectAttributes attributes) {
        boolean success = siteSettingService.deleteSiteSetting(id);
        
        if (success) {
            attributes.addFlashAttribute("message", "删除成功");
        } else {
            attributes.addFlashAttribute("message", "删除失败");
        }
        
        return "redirect:/admin/settings";
    }
    
    /**
     * 批量更新网站设置（通过表单提交的方式）
     */
    @PostMapping("/update")
    public String update(@RequestParam Map<String, String> params, RedirectAttributes attributes) {
        Map<String, String> settings = new HashMap<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry.getKey().startsWith("setting_")) {
                String key = entry.getKey().substring("setting_".length());
                settings.put(key, entry.getValue());
            }
        }
        
        boolean success = siteSettingService.updateSiteSettingValues(settings);
        
        if (success) {
            attributes.addFlashAttribute("message", "更新成功");
        } else {
            attributes.addFlashAttribute("message", "更新失败");
        }
        
        return "redirect:/admin/settings";
    }
}