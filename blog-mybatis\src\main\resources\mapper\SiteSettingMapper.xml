<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.czc.blog.mapper.SiteSettingMapper">
  <resultMap id="BaseResultMap" type="com.czc.blog.po.SiteSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="setting_key" jdbcType="VARCHAR" property="settingKey" />
    <result column="setting_value" jdbcType="VARCHAR" property="settingValue" />
    <result column="setting_description" jdbcType="VARCHAR" property="settingDescription" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  
  <sql id="Base_Column_List">
    id, setting_key, setting_value, setting_description, create_time, update_time
  </sql>
  
  <select id="selectByExample" parameterType="com.czc.blog.po.SiteSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_site_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_site_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectBySettingKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_site_setting
    where setting_key = #{settingKey,jdbcType=VARCHAR}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_site_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteByExample" parameterType="com.czc.blog.po.SiteSettingExample">
    delete from t_site_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  
  <insert id="insert" parameterType="com.czc.blog.po.SiteSetting">
    insert into t_site_setting (id, setting_key, setting_value, 
      setting_description, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{settingKey,jdbcType=VARCHAR}, #{settingValue,jdbcType=VARCHAR}, 
      #{settingDescription,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  
  <insert id="insertSelective" parameterType="com.czc.blog.po.SiteSetting">
    insert into t_site_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="settingKey != null">
        setting_key,
      </if>
      <if test="settingValue != null">
        setting_value,
      </if>
      <if test="settingDescription != null">
        setting_description,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="settingKey != null">
        #{settingKey,jdbcType=VARCHAR},
      </if>
      <if test="settingValue != null">
        #{settingValue,jdbcType=VARCHAR},
      </if>
      <if test="settingDescription != null">
        #{settingDescription,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <select id="countByExample" parameterType="com.czc.blog.po.SiteSettingExample" resultType="java.lang.Long">
    select count(*) from t_site_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <update id="updateByExampleSelective" parameterType="map">
    update t_site_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.settingKey != null">
        setting_key = #{record.settingKey,jdbcType=VARCHAR},
      </if>
      <if test="record.settingValue != null">
        setting_value = #{record.settingValue,jdbcType=VARCHAR},
      </if>
      <if test="record.settingDescription != null">
        setting_description = #{record.settingDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByExample" parameterType="map">
    update t_site_setting
    set id = #{record.id,jdbcType=BIGINT},
      setting_key = #{record.settingKey,jdbcType=VARCHAR},
      setting_value = #{record.settingValue,jdbcType=VARCHAR},
      setting_description = #{record.settingDescription,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.czc.blog.po.SiteSetting">
    update t_site_setting
    <set>
      <if test="settingKey != null">
        setting_key = #{settingKey,jdbcType=VARCHAR},
      </if>
      <if test="settingValue != null">
        setting_value = #{settingValue,jdbcType=VARCHAR},
      </if>
      <if test="settingDescription != null">
        setting_description = #{settingDescription,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.czc.blog.po.SiteSetting">
    update t_site_setting
    set setting_key = #{settingKey,jdbcType=VARCHAR},
      setting_value = #{settingValue,jdbcType=VARCHAR},
      setting_description = #{settingDescription,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>