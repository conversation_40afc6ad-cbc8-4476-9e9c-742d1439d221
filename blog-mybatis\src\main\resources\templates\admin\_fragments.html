<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:fragment="head(title)">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"></meta>
    <link rel="icon" href="../static/images/tubiao.jpg" type="image/jpg">
    <title>博客发布</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/semantic-ui@2.4.2/dist/semantic.min.css">
    <link rel="stylesheet" href="../../static/css/me.css" th:href="@{/css/me.css}">
    <link rel="stylesheet" href="../../static/lib/editormd/css/editormd.min.css"
          th:href="@{/lib/editormd/css/editormd.min.css}">

    <style>
        /* 假设这是您用于隐藏和显示导航栏的CSS类 */
        .hidden-nav2 {
            top: -100%; /* 根据您的导航栏高度调整 */

        }
        .attached.segment.myElement2 {
            position: fixed;
            top: 0em;
            z-index: 99999;
            height: 5em;
            /* 其他样式属性 */
            transition: top 3s ease;
        }
    </style>
</head>
<body>

<!--导航-->
<nav th:fragment="menu(n)" class="ui inverted attached segment m-padded-tb-mini myElement2">
    <div class="ui container">
        <div class="ui inverted secondary stackable menu" style="background:#1B1C1D">
            <h2 class="ui teal header item">管理后台</h2>
            <a href="#" th:href="@{/admin/blogs}" class="m-item item m-mobile-hide" th:classappend="${n==1} ? 'active'"><i
                    class="home icon"></i>博客</a>
            <a href="#" th:href="@{/admin/types}" class="m-item item m-mobile-hide" th:classappend="${n==2} ? 'active'"><i
                    class="idea icon"></i>分类</a>
            <a href="#" th:href="@{/admin/tags}" class="m-item item m-mobile-hide"
               th:classappend="${n==3} ? 'active'"><i class="tags icon"></i>标签</a>
            <a href="#" th:href="@{/admin/labels}" class="m-item item m-mobile-hide"
               th:classappend="${n==5} ? 'active'"><i class="tag icon"></i>个人标签</a>
            <a href="#" th:href="@{/admin/sociallinks}" class="m-item item m-mobile-hide"
               th:classappend="${n==6} ? 'active'"><i class="linkify icon"></i>社交链接</a>
            <a href="#" th:href="@{/admin/settings}" class="m-item item m-mobile-hide" 
               th:classappend="${n==4} ? 'active'"><i class="settings icon"></i>设置</a>
            <div class="right menu m-item m-mobile-hide">
                <div class="ui dropdown item">
                    <div class="text">
                        <img src="" alt="" th:src="${#session.getAttribute('user').avatar}" class="ui avatar image">
                        CZC
                    </div>
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a href="#" th:href="@{/admin/logout}" class="item">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <a href="#" class="ui menu toggle black icon button m-top-right m-mobile-show">
        <i class="sidebar icon"></i>
    </a>
</nav>

<!--底部-->
<footer th:fragment="footer" class="ui inverted vertical segment m-padded-tb-massive">
    <div class="ui center aligned container">
        <div class="ui stackable inverted divided grid">
            <div class="three wide column">
                <div class="ui inverted link list">
                    <img src="../static/images/wechat.png" th:src="@{/images/wechat.png}" class="ui rounded image"
                         alt="" style="width: 120px;display: inline-block;text-align: center">
                </div>
            </div>

            <div class="ten wide column">
                <h4 class="ui inverted header">Blog</h4>
                <p>This is my personal blog！</p>
            </div>
        </div>
        <div class="ui inverted section divider"></div>
        <p> Copyright  ©  2020  <a href="#" style="color: #fff">CZC</a>  版权所有  <a style="text-decoration: none; color: #fff;" href="https://beian.miit.gov.cn" target="_blank">粤ICP备2024342065号-1</a > </p>
    </div>

</footer>
<th:block th:fragment="script">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.2/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/semantic-ui/2.2.4/semantic.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/jquery.scrollto@2.1.2/jquery.scrollTo.min.js"></script>
    <script src="../static/lib/prism/prism.js" th:src="@{/lib/prism/prism.js}"></script>
    <script src="../static/lib/tocbot/tocbot.min.js" th:src="@{/lib/tocbot/tocbot.min.js}"></script>
    <script>
        $('#newblog-container').load(/*[[@{/footer/newBlog}]]*/"/footer/newBlog");
    </script>
<script>
    $(document).ready(function() {
        var lastScrollY = window.scrollY;
        var $nav = $('nav.ui.inverted.attached.segment');

        $(window).scroll(function() {
            var currentScrollY = window.scrollY;
            if (currentScrollY > lastScrollY) {
                // 向下滚动
                $nav.removeClass('myElement2').addClass('hidden-nav2');
            } else if (currentScrollY < lastScrollY){
                // 向上滚动
                $nav.removeClass('hidden-nav2').addClass('myElement2');
            }
            lastScrollY = currentScrollY;
        });

        // 加载新博客列表的脚本（保持不变）
        $('#newblog-container').load(/*[[@{/footer/newBlog}]]*/"/footer/newBlog");
    });
</script>

</th:block>

</body>
</html>