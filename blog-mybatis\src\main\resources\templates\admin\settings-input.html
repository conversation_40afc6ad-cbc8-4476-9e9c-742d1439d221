<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:replace="admin/_fragments :: head(~{::title})">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"></meta>
    <link rel="icon" href="../static/images/tubiao.jpg" type="image/jpg">
    <title>网站设置编辑</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/semantic-ui@2.4.2/dist/semantic.min.css">
    <link rel="stylesheet" href="../../static/css/me.css">
</head>
<body>

<!--导航-->
<nav th:replace="admin/_fragments :: menu(4)" class="ui inverted attached segment m-padded-tb-mini">
    <div class="ui container" >
        <div class="ui inverted secondary stackable menu">
            <h2 class="ui teal header item">管理后台</h2>
            <a href="#" class="active m-item item m-mobile-hide"><i class="home icon"></i>博客</a>
            <a href="#" class="m-item item m-mobile-hide"><i class="idea icon"></i>分类</a>
            <a href="#" class="m-item item m-mobile-hide"><i class="tags icon"></i>标签</a>
            <a href="#" class="m-item item m-mobile-hide"><i class="settings icon"></i>设置</a>
            <div class="right menu m-item m-mobile-hide">
                <div class="ui dropdown item">
                    <div class="text">
                        <img src="" th:src="${#session.getAttribute('user').avatar}" alt="" class="ui avatar image">
                        <span th:text="${#session.getAttribute('user').nickname}">CZC</span>
                    </div>
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a href="#" th:href="@{/admin/logout}" class="item">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <a href="#" class="ui menu toggle black icon button m-top-right m-mobile-show">
        <i class="sidebar icon"></i>
    </a>
</nav>
<div class="ui attached pointing menu">
    <div class="ui container" style="margin-top: 5rem">
        <div class="right menu">
            <a href="#" th:href="@{/admin/settings/input}" class="active teal item">新增</a>
            <a href="#" th:href="@{/admin/settings}" class="teal item">列表</a>
        </div>
    </div>
</div>

<!--主体-->
<div class="m-container-small m-padded-tb-big">
    <div class="ui container">
        <form method="post" th:object="${siteSetting}" th:action="@{/admin/settings/post}" class="ui form">
            <input type="hidden" name="id" th:value="*{id}">
            
            <div class="required field">
                <label>键名</label>
                <input type="text" name="settingKey" th:value="*{settingKey}" placeholder="设置项键名">
            </div>
            
            <div class="required field">
                <label>值</label>
                <textarea name="settingValue" th:text="*{settingValue}" placeholder="设置项值"></textarea>
            </div>
            
            <div class="required field">
                <label>描述</label>
                <input type="text" name="settingDescription" th:value="*{settingDescription}" placeholder="设置项描述">
            </div>
            
            <div class="ui error message"></div>
            
            <div class="ui right aligned container">
                <button type="button" onclick="window.history.go(-1)" class="ui button">返回</button>
                <button type="submit" class="ui teal submit button">提交</button>
            </div>
        </form>
    </div>
</div>

<br>
<br>
<!--底部-->
<footer th:replace="admin/_fragments :: footer" class="ui inverted vertical segment m-padded-tb-massive">
    <div class="ui center aligned container">
        <div class="ui stackable inverted divided grid">
            <div class="three wide column">
                <div class="ui inverted link list">
                    <img src="../static/images/wechat.jpg" class="ui rounded image" alt="" style="width: 120px">
                </div>
            </div>
            <div class="ten wide column">
                <h4 class="ui inverted header">Blog</h4>
                <p>This is my personal blog！</p>
            </div>
        </div>
        <div class="ui inverted section divider"></div>
        <p> Copyright  ©  2020  <a href="#" style="color: #fff">CZC</a>  版权所有  <a style="text-decoration: none; color: #fff;" href="https://beian.miit.gov.cn" target="_blank">粤ICP备2024342065号-1</a > </p>
    </div>
</footer>

<!--/*/<th:block th:replace="admin/_fragments :: script">/*/-->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.2/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/semantic-ui/2.2.4/semantic.min.js"></script>
<!--/*/</th:block>/*/-->

<script>
    $('.menu.toggle').click(function () {
        $('.m-item').toggleClass('m-mobile-hide');
    });

    $('.ui.dropdown').dropdown({
        on : 'hover'
    });

    // 表单验证
    $('.ui.form').form({
        fields: {
            settingKey: {
                identifier: 'settingKey',
                rules: [{
                    type: 'empty',
                    prompt: '请输入设置项键名'
                }]
            },
            settingValue: {
                identifier: 'settingValue',
                rules: [{
                    type: 'empty',
                    prompt: '请输入设置项值'
                }]
            },
            settingDescription: {
                identifier: 'settingDescription',
                rules: [{
                    type: 'empty',
                    prompt: '请输入设置项描述'
                }]
            }
        }
    });
</script>
</body>
</html>