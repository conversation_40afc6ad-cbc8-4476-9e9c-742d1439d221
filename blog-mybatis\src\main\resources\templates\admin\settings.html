<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:replace="admin/_fragments :: head(~{::title})">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"></meta>
    <link rel="icon" href="../static/images/tubiao.jpg" type="image/jpg">
    <title>网站设置管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/semantic-ui@2.4.2/dist/semantic.min.css">
    <link rel="stylesheet" href="../../static/css/me.css">
</head>
<body>

<!--导航-->
<nav th:replace="admin/_fragments :: menu(4)" class="ui inverted attached segment m-padded-tb-mini">
    <div class="ui container" >
        <div class="ui inverted secondary stackable menu">
            <h2 class="ui teal header item">管理后台</h2>
            <a href="#" class="active m-item item m-mobile-hide"><i class="home icon"></i>博客</a>
            <a href="#" class="m-item item m-mobile-hide"><i class="idea icon"></i>分类</a>
            <a href="#" class="m-item item m-mobile-hide"><i class="tags icon"></i>标签</a>
            <a href="#" class="m-item item m-mobile-hide"><i class="settings icon"></i>设置</a>
            <div class="right menu m-item m-mobile-hide">
                <div class="ui dropdown item">
                    <div class="text">
                        <img src="" th:src="${#session.getAttribute('user').avatar}" alt="" class="ui avatar image">
                        <span th:text="${#session.getAttribute('user').nickname}">CZC</span>
                    </div>
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a href="#" th:href="@{/admin/logout}" class="item">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <a href="#" class="ui menu toggle black icon button m-top-right m-mobile-show">
        <i class="sidebar icon"></i>
    </a>
</nav>
<div class="ui attached pointing menu">
    <div class="ui container" style="margin-top: 5rem">
        <div class="right menu">
            <a href="#" th:href="@{/admin/settings/input}" class="teal item">新增</a>
            <a href="#" th:href="@{/admin/settings}" class="active teal item">列表</a>
        </div>
    </div>
</div>

<!--主体-->
<div class="m-container-big m-padded-tb-big">
    <div class="ui container">
        <div class="ui success message" th:unless="${#strings.isEmpty(message)}">
            <i class="close icon"></i>
            <div class="header">提示：</div>
            <p th:text="${message}">操作成功！</p>
        </div>
        
        <!-- 编辑表单 -->
        <form method="post" th:action="@{/admin/settings/update}" class="ui form">
            <h4 class="ui dividing header">网站基本设置</h4>
            
            <div class="field">
                <label>博客标题</label>
                <input type="text" name="setting_blog_title" th:value="${#request.getAttribute('blog_title')}">
            </div>
            
            <div class="field">
                <label>博客描述</label>
                <input type="text" name="setting_blog_description" th:value="${#request.getAttribute('blog_description')}">
            </div>
            
            <div class="field">
                <label>联系邮箱</label>
                <input type="text" name="setting_email" th:value="${#request.getAttribute('email')}">
            </div>
            
            <div class="field">
                <label>版权信息</label>
                <textarea name="setting_copyright" th:text="${#request.getAttribute('copyright')}"></textarea>
            </div>
            
            <div class="field">
                <label>关于我页面文字</label>
                <textarea name="setting_about_me_text" th:text="${#request.getAttribute('about_me_text')}"></textarea>
            </div>
            
            <div class="ui right aligned container">
                <button type="submit" class="ui teal submit button">保存</button>
            </div>
        </form>
        
        <div class="ui divider"></div>
        
        <!-- 设置列表 -->
        <table class="ui compact teal table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>键名</th>
                    <th>值</th>
                    <th>描述</th>
                    <th>更新时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="siteSetting,iterStat : ${page.list}">
                    <td th:text="${siteSetting.id}">1</td>
                    <td th:text="${siteSetting.settingKey}">email</td>
                    <td th:text="${siteSetting.settingValue}"><EMAIL></td>
                    <td th:text="${siteSetting.settingDescription}">联系邮箱</td>
                    <td th:text="${#dates.format(siteSetting.updateTime,'yyyy-MM-dd HH:mm')}">2020-01-01 00:00</td>
                    <td>
                        <a href="#" th:href="@{/admin/settings/{id}/input(id=${siteSetting.id})}" class="ui mini teal basic button">编辑</a>
                        <a href="#" th:href="@{/admin/settings/{id}/delete(id=${siteSetting.id})}" onclick="return confirm('确定要删除该设置吗？')" class="ui mini red basic button">删除</a>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="6">
                        <div class="ui mini pagination menu" th:if="${page.pages}>1">
                            <a class="item" th:href="@{/admin/settings(page=${page.pageNum}-1)}" th:unless="${page.isFirstPage}">上一页</a>
                            <a class="item" th:href="@{/admin/settings(page=${page.pageNum}+1)}" th:unless="${page.isLastPage}">下一页</a>
                        </div>
                        <a href="#" th:href="@{/admin/settings/input}" class="ui mini right floated teal basic button">新增设置</a>
                    </th>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

<br>
<br>
<!--底部-->
<footer th:replace="admin/_fragments :: footer" class="ui inverted vertical segment m-padded-tb-massive">
    <div class="ui center aligned container">
        <div class="ui stackable inverted divided grid">
            <div class="three wide column">
                <div class="ui inverted link list">
                    <img src="../static/images/wechat.jpg" class="ui rounded image" alt="" style="width: 120px">
                </div>
            </div>
            <div class="ten wide column">
                <h4 class="ui inverted header">Blog</h4>
                <p>This is my personal blog！</p>
            </div>
        </div>
        <div class="ui inverted section divider"></div>
        <p> Copyright  ©  2020  <a href="#" style="color: #fff">CZC</a>  版权所有  <a style="text-decoration: none; color: #fff;" href="https://beian.miit.gov.cn" target="_blank">粤ICP备2024342065号-1</a > </p>
    </div>

</footer>

<!--/*/<th:block th:replace="admin/_fragments :: script">/*/-->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.2/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/semantic-ui/2.2.4/semantic.min.js"></script>
<!--/*/</th:block>/*/-->

<script>
    $('.menu.toggle').click(function () {
        $('.m-item').toggleClass('m-mobile-hide');
    });

    $('.ui.dropdown').dropdown({
        on : 'hover'
    });

    // 消息关闭功能
    $('.message .close').on('click', function () {
        $(this).closest('.message').transition('fade');
    });
</script>
</body>
</html>